name: Bug/Crash Report
description: Create a bug or crash report for Vencord. ALWAYS FIRST USE OUR SUPPORT CHANNEL! ONLY USE THIS FORM IF YOU ARE A CONTRIBUTOR OR WERE TOLD TO DO SO IN THE SUPPORT CHANNEL.
labels: [bug]
title: "[Bug] <title>"

body:
    - type: markdown
      attributes:
          value: |
              ![Are you a developer? No? This form is not for you!](https://github.com/Vendicated/Vencord/blob/main/.github/ISSUE_TEMPLATE/developer-banner.png?raw=true)

              GitHub Issues are for development, not support! Please use our [support server](https://vencord.dev/discord) unless you are a Vencord Developer.

    - type: textarea
      id: bug-description
      attributes:
          label: What happens when the bug or crash occurs?
          description: Where does this bug or crash occur, when does it occur, etc.
          placeholder: The bug/crash happens sometimes when I do ..., causing this to not work/the app to crash. I think it happens because of ...
      validations:
          required: true

    - type: textarea
      id: expected-behaviour
      attributes:
          label: What is the expected behaviour?
          description: Simply detail what the expected behaviour is.
          placeholder: I expect Vencord/Discord to open the ... page instead of ..., it prevents me from doing ...
      validations:
          required: true

    - type: textarea
      id: steps-to-take
      attributes:
          label: How do you recreate this bug or crash?
          description: Give us a list of steps in order to recreate the bug or crash.
          placeholder: |
              1. Do ...
              2. Then ...
              3. Do this ..., ... and then ...
              4. Observe "the bug" or "the crash"
      validations:
          required: true

    - type: textarea
      id: crash-log
      attributes:
          label: Errors
          description: Open the Developer Console with Ctrl/Cmd + Shift + i. Then look for any red errors (Ignore network errors like Failed to load resource) and paste them between the "```".
          value: |
              ```
              Replace this text with your crash-log.
              ```
      validations:
          required: false

    - type: checkboxes
      id: agreement-check
      attributes:
          label: Request Agreement
          description: We only accept reports for bugs that happen on Discord Stable. Canary and PTB are Development branches and may be unstable
          options:
              - label: I am using Discord Stable or tried on Stable and this bug happens there as well
                required: true
              - label: I am a Vencord Developer
                required: true
