[{"id": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "content-security-policy", "operation": "remove"}, {"header": "content-security-policy-report-only", "operation": "remove"}]}, "condition": {"resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 2, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "content-type", "operation": "set", "value": "text/css"}]}, "condition": {"resourceTypes": ["stylesheet"], "urlFilter": "https://raw.githubusercontent.com/*"}}]