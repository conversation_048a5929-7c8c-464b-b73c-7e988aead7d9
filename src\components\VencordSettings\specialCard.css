.vc-donate-button {
    overflow: visible !important;
}

.vc-donate-button .vc-heart-icon {
    transition: transform 0.3s;
}

.vc-donate-button:hover .vc-heart-icon {
    transform: scale(1.1);
    z-index: 10;
    position: relative;
}

.vc-settings-card {
    padding: 1em;
    margin-bottom: 1em;
}

.vc-special-card-special {
    padding: 1em 1.5em;
    margin-bottom: 1em;
    background-size: cover;
    background-position: center;
}

.vc-special-card-flex {
    display: flex;
    flex-direction: row;
}

.vc-special-card-flex-main {
    width: 100%;
}

.vc-special-title {
    color: black;
}

.vc-special-subtitle {
    color: black;
    font-size: 1.2em;
    font-weight: bold;
    margin-top: 0.5em;
}

.vc-special-text {
    color: black;
    font-size: 1em;
    margin-top: .75em;
    white-space: pre-line;
}

.vc-special-seperator {
    margin-top: .75em;
    border-top: 1px solid white;
    opacity: 0.4;
}

.vc-special-hyperlink {
    margin-top: 1em;
    cursor: pointer;

    .vc-special-hyperlink-text {
        color: black;
        font-size: 1em;
        font-weight: bold;
        text-align: center;
        transition: text-decoration 0.5s;
        cursor: pointer;
    }

    &:hover .vc-special-hyperlink-text {
        text-decoration: underline;
    }
}

.vc-special-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 1em;
    flex-shrink: 0;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: white;
}

.vc-special-image {
    width: 65%;
}
