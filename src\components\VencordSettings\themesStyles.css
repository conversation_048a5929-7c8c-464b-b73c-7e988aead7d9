.vc-settings-theme-grid {
    display: grid;
    grid-gap: 16px;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.vc-settings-theme-card {
    display: flex;
    flex-direction: column;
    background-color: var(--background-secondary-alt);
    color: var(--interactive-active);
    border-radius: 8px;
    padding: 1em;
    width: 100%;
    transition: 0.1s ease-out;
    transition-property: box-shadow, transform, background, opacity;
}

.vc-settings-theme-card-text {
    text-overflow: ellipsis;
    height: 1.2em;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
}

.vc-settings-theme-author::before {
    content: "by ";
}
