name: Test Patches
on:
    workflow_dispatch:
        inputs:
            discord_branch:
                type: choice
                description: "Discord Branch to test patches on"
                options:
                    - both
                    - stable
                    - canary
                default: both
            webhook_url:
                type: string
                description: "Webhook URL that the report will be posted to. This will be visible for everyone, so DO NOT pass sensitive webhooks like discord webhook. This is meant to be used by Venbot."
                required: false
    # schedule:
    #   # Every day at midnight
    #   - cron: 0 0 * * *

jobs:
    TestPlugins:
        if: github.repository == 'Vendicated/Vencord'
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v4
              if: ${{ github.event_name == 'schedule' }}
              with:
                  ref: dev

            - uses: actions/checkout@v4
              if: ${{ github.event_name == 'workflow_dispatch' }}

            - uses: pnpm/action-setup@v3 # Install pnpm using packageManager key in package.json

            - name: Use Node.js 20
              uses: actions/setup-node@v4
              with:
                  node-version: 20
                  cache: "pnpm"

            - name: Install dependencies
              run: |
                  pnpm install --frozen-lockfile

            - name: Install Google Chrome
              id: setup-chrome
              uses: browser-actions/setup-chrome@82b9ce628cc5595478a9ebadc480958a36457dc2
              with:
                  chrome-version: stable

            - name: Build Vencord Reporter Version
              run: pnpm buildReporter

            - name: Run Reporter
              timeout-minutes: 10
              run: |
                  export PATH="$PWD/node_modules/.bin:$PATH"
                  export CHROMIUM_BIN=${{ steps.setup-chrome.outputs.chrome-path }}

                  esbuild scripts/generateReport.ts > dist/report.mjs

                  stable_output_file=$(mktemp)
                  canary_output_file=$(mktemp)

                  pids=""

                  branch="${{ inputs.discord_branch }}"
                  if [[ "${{ github.event_name }}" = "schedule" ]]; then
                    branch="both"
                  fi

                  if [[ "$branch" = "both" || "$branch" = "stable" ]]; then
                    node dist/report.mjs > "$stable_output_file" &
                    pids+=" $!"
                  fi

                  if [[ "$branch" = "both" || "$branch" = "canary" ]]; then
                    USE_CANARY=true node dist/report.mjs > "$canary_output_file" &
                    pids+=" $!"
                  fi

                  exit_code=0
                  for pid in $pids; do
                      if ! wait "$pid"; then
                        exit_code=1
                      fi
                  done

                  cat "$stable_output_file" "$canary_output_file" >> $GITHUB_STEP_SUMMARY
                  exit $exit_code
              env:
                  WEBHOOK_URL: ${{ inputs.webhook_url || secrets.DISCORD_WEBHOOK }}
                  WEBHOOK_SECRET: ${{ secrets.WEBHOOK_SECRET }}
