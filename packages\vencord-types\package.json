{"name": "@vencord/types", "private": false, "version": "1.11.5", "description": "", "types": "index.d.ts", "scripts": {"prepublishOnly": "tsx ./prepare.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "Vencord", "license": "GPL-3.0", "devDependencies": {"@types/fs-extra": "^11.0.4", "fs-extra": "^11.3.0", "tsx": "^4.19.2"}, "dependencies": {"@types/lodash": "4.17.15", "@types/node": "^22.13.4", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "discord-types": "^1.3.26", "standalone-electron-types": "^34.2.0", "type-fest": "^4.35.0"}}