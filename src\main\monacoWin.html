<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <title>Vencord QuickCSS Editor</title>
        <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/monaco-editor@0.50.0/min/vs/editor/editor.main.css"
            integrity="sha256-tiJPQ2O04z/pZ/AwdyIghrOMzewf+PIvEl1YKbQvsZk="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"
        />
        <style>
            html,
            body,
            #container {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
                overflow: hidden;
            }
        </style>
    </head>

    <body>
        <div id="container"></div>
        <script
            src="https://cdn.jsdelivr.net/npm/monaco-editor@0.50.0/min/vs/loader.js"
            integrity="sha256-KcU48TGr84r7unF7J5IgBo95aeVrEbrGe04S7TcFUjs="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"
        ></script>

        <script>
            require.config({
                paths: {
                    vs: "https://cdn.jsdelivr.net/npm/monaco-editor@0.50.0/min/vs",
                },
            });

            require(["vs/editor/editor.main"], () => {
                getCurrentCss().then((css) => {
                    var editor = monaco.editor.create(
                        document.getElementById("container"),
                        {
                            value: css,
                            language: "css",
                            theme: getTheme(),
                        }
                    );
                    editor.onDidChangeModelContent(() =>
                        setCss(editor.getValue())
                    );
                    window.addEventListener("resize", () => {
                        // make monaco re-layout
                        editor.layout();
                    });
                });
            });
        </script>
    </body>
</html>
