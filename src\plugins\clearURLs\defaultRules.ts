/*
 * Vencord, a modification for <PERSON><PERSON>'s desktop app
 * Copyright (c) 2022 Vendicated and contributors
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
*/

export const defaultRules = [
    "action_object_map",
    "action_type_map",
    "action_ref_map",
    "spm@*.aliexpress.com",
    "scm@*.aliexpress.com",
    "aff_platform",
    "aff_trace_key",
    "algo_expid@*.aliexpress.*",
    "algo_pvid@*.aliexpress.*",
    "btsid",
    "ws_ab_test",
    "pd_rd_*@amazon.*",
    "_encoding@amazon.*",
    "psc@amazon.*",
    "tag@amazon.*",
    "ref_@amazon.*",
    "pf_rd_*@amazon.*",
    "pf@amazon.*",
    "crid@amazon.*",
    "keywords@amazon.*",
    "sprefix@amazon.*",
    "sr@amazon.*",
    "ie@amazon.*",
    "node@amazon.*",
    "qid@amazon.*",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "sc_cid",
    "mkt_tok",
    "trk",
    "trkCampaign",
    "ga_*",
    "gclid",
    "gclsrc",
    "hmb_campaign",
    "hmb_medium",
    "hmb_source",
    "spReportId",
    "spJobID",
    "spUserID",
    "spMailingID",
    "itm_*",
    "s_cid",
    "elqTrackId",
    "elqTrack",
    "assetType",
    "assetId",
    "recipientId",
    "campaignId",
    "siteId",
    "mc_cid",
    "mc_eid",
    "pk_*",
    "sc_campaign",
    "sc_channel",
    "sc_content",
    "sc_medium",
    "sc_outcome",
    "sc_geo",
    "sc_country",
    "nr_email_referer",
    "vero_conv",
    "vero_id",
    "yclid",
    "_openstat",
    "mbid",
    "cmpid",
    "cid",
    "c_id",
    "campaign_id",
    "Campaign",
    "hash@ebay.*",
    "fb_action_ids",
    "fb_action_types",
    "fb_ref",
    "fb_source",
    "fbclid",
    "<EMAIL>",
    "<EMAIL>",
    "gs_l",
    "gs_lcp@google.*",
    "ved@google.*",
    "ei@google.*",
    "sei@google.*",
    "gws_rd@google.*",
    "gs_gbg@google.*",
    "gs_mss@google.*",
    "gs_rn@google.*",
    "_hsenc",
    "_hsmi",
    "__hssc",
    "__hstc",
    "hsCtaTracking",
    "<EMAIL>",
    "<EMAIL>",
    "t@*.twitter.com",
    "s@*.twitter.com",
    "ref_*@*.twitter.com",
    "t@*.x.com",
    "s@*.x.com",
    "ref_*@*.x.com",
    "t@*.fixupx.com",
    "s@*.fixupx.com",
    "ref_*@*.fixupx.com",
    "t@*.fxtwitter.com",
    "s@*.fxtwitter.com",
    "ref_*@*.fxtwitter.com",
    "t@*.twittpr.com",
    "s@*.twittpr.com",
    "ref_*@*.twittpr.com",
    "t@*.fixvx.com",
    "s@*.fixvx.com",
    "ref_*@*.fixvx.com",
    "tt_medium",
    "tt_content",
    "lr@yandex.*",
    "redircnt@yandex.*",
    "feature@*.youtube.com",
    "kw@*.youtube.com",
    "si@*.youtube.com",
    "pp@*.youtube.com",
    "si@*.youtu.be",
    "wt_zmc",
    "utm_source",
    "utm_content",
    "utm_medium",
    "utm_campaign",
    "utm_term",
    "<EMAIL>",
    "igshid",
    "igsh",
    "<EMAIL>",
    "<EMAIL>",
];
