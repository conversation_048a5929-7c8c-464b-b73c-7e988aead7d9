{
    // this allows you to debug Vencord from VSCode.
    // How to use:
    // You need to run Discord via the command line to pass some flags to it.
    // If you want to debug the main (node.js) process (preload.ts, ipcMain/*, patcher.ts),
    //     add the --inspect flag
    // To debug the renderer (99% of Vencord), add the --remote-debugging-port=9223 flag
    //
    // Now launch the desired configuration in VSCode and start Discord with the flags.
    // For example, to debug both process, run Electron: All then launch Discord with
    // discord --remote-debugging-port=9223 --inspect

    "version": "0.2.0",
    "configurations": [
        {
            "name": "Electron: Main",
            "type": "node",
            "request": "attach",
            "port": 9229,
            "timeout": 30000
        },
        {
            "name": "Electron: Renderer",
            "type": "chrome",
            "request": "attach",
            "port": 9223,
            "timeout": 30000,
            "webRoot": "${workspaceFolder}/src"
        }
    ],
    "compounds": [
        {
            "name": "Electron: All",
            "configurations": ["Electron: Main", "Electron: Renderer"]
        }
    ]
}
