.vc-addon-card {
    background-color: var(--background-secondary-alt);
    color: var(--interactive-active);
    border-radius: 8px;
    display: block;
    height: 100%;
    padding: 12px;
    width: 100%;
    transition: 0.1s ease-out;
    transition-property: box-shadow, transform, background, opacity;
    box-sizing: border-box;
}

.visual-refresh .vc-addon-card {
    background-color: var(--card-primary-bg);
    border: 1px solid var(--border-subtle);
}

.vc-addon-card-disabled {
    opacity: 0.6;
}

.vc-addon-card:hover {
    background-color: var(--background-tertiary);
    transform: translateY(-1px);
    box-shadow: var(--elevation-high);
}

.visual-refresh .vc-addon-card:hover {
    /* same as non-hover, here to overwrite the non-refresh hover background */
    background-color: var(--card-primary-bg);
}

.vc-addon-header {
    margin-top: auto;
    display: flex;
    width: 100%;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    margin-bottom: 0.5em;
}

.vc-addon-note {
    height: 36px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    /* stylelint-disable-next-line property-no-unknown */
    box-orient: vertical;
}

.vc-addon-name-author {
    width: 100%;
}

.vc-addon-name {
    display: flex;
    width: 100%;
    align-items: center;
    flex-grow: 1;
    gap: 8px;
}

.vc-addon-author {
    font-size: 0.8em;
}

.vc-addon-author::before {
    content: "by ";
}

.vc-addon-title-container {
    width: 100%;
    overflow: hidden;
    height: 1.25em;
    position: relative;
}

.vc-addon-title {
    position: absolute;
    inset: 0;
    overflow: hidden;
    text-overflow: ellipsis;
}

@keyframes vc-addon-title {
    0% {
        transform: translateX(0);
    }

    50% {
        transform: translateX(var(--offset));
    }

    100% {
        transform: translateX(0);
    }
}

.vc-addon-title:hover {
    overflow: visible;
    animation: vc-addon-title var(--duration) linear infinite;
}
