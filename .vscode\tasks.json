{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Build",
            "type": "shell",
            "command": "pnpm build",
            "group": {
                "kind": "build",
                "isDefault": true
            }
        },
        {
            "label": "Watch",
            "type": "shell",
            "command": "pnpm watch",
            "problemMatcher": [],
            "group": {
                "kind": "build"
            }
        }
    ]
}