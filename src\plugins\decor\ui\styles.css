.vc-decor-danger-btn {
    color: var(--white-500);
    background-color: var(--button-danger-background);
}

.vc-decor-change-decoration-modal-content {
    position: relative;
    display: flex;
    border-radius: 5px 5px 0 0;
    padding: 0 16px;
    gap: 4px;
}

.vc-decor-change-decoration-modal-preview {
    display: flex;
    flex-direction: column;
    margin-top: 24px;
    gap: 8px;
    max-width: 280px;
}

.vc-decor-change-decoration-modal-decoration {
    width: 80px;
    height: 80px;
}

.vc-decor-change-decoration-modal-footer {
    justify-content: space-between;
}

.vc-decor-change-decoration-modal-footer-btn-container {
    display: flex;
    flex-direction: row-reverse;
}

.vc-decor-create-decoration-modal-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 0 16px;
}

.vc-decor-create-decoration-modal-form-preview-container {
    display: flex;
    gap: 16px;
}

.vc-decor-modal-header {
    padding: 16px;
}

.vc-decor-modal-footer {
    padding: 16px;
}

.vc-decor-create-decoration-modal-form {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    gap: 16px;
}

.vc-decor-sectioned-grid-list-container {
    display: flex;
    flex-direction: column;
    overflow: hidden scroll;
    max-height: 512px;
    width: 352px; /* ((80 + 8 (grid gap)) * desired columns) (scrolled takes the extra 8 padding off conveniently) */
    gap: 12px;
}

.vc-decor-sectioned-grid-list-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.vc-decor-section-remove-margin {
    margin-bottom: 0;
}
