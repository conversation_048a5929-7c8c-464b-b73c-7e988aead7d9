/*
 * Vencord, a Discord client mod
 * Copyright (c) 2024 Vendicated and contributors
 * SPDX-License-Identifier: GPL-3.0-or-later
 */

import { Dev<PERSON> } from "@utils/constants";
import definePlugin from "@utils/types";


export default definePlugin({
    name: "DynamicImageModalAPI",
    authors: [<PERSON><PERSON><PERSON><PERSON>an, <PERSON><PERSON>.Nuckyz],
    description: "Allows you to omit either width or height when opening an image modal",
    patches: [
        {
            find: ".dimensionlessImage,",
            replacement: {
                match: /(?<="IMAGE"===\i&&\(\i=)\i(?=\?)/,
                replace: "true"
            }
        }
    ]
});
