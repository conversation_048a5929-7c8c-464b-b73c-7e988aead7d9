name: Release Browser Extension
on:
    push:
        tags:
            - v*

jobs:
    Publish:
        if: github.repository == 'Vendicated/Vencord'
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v4

            - name: check that tag matches package.json version
              run: |
                  pkg_version="v$(jq -r .version < package.json)"
                  if [[ "${{ github.ref_name }}" != "$pkg_version" ]]; then
                      echo "Tag ${{ github.ref_name }} does not match package.json version $pkg_version" >&2
                      exit 1
                  fi

            - uses: pnpm/action-setup@v3 # Install pnpm using packageManager key in package.json

            - name: Use Node.js 19
              uses: actions/setup-node@v4
              with:
                  node-version: 20
                  cache: "pnpm"

            - name: Install dependencies
              run: pnpm install --frozen-lockfile

            - name: Build web
              run: pnpm buildWebStandalone

            - name: Publish extension
              run: |
                  cd dist/chromium-unpacked
                  pnpx chrome-webstore-upload-cli@2.1.0 upload --auto-publish
              env:
                  EXTENSION_ID: ${{ secrets.CHROME_EXTENSION_ID }}
                  CLIENT_ID: ${{ secrets.CHROME_CLIENT_ID }}
                  CLIENT_SECRET: ${{ secrets.CHROME_CLIENT_SECRET }}
                  REFRESH_TOKEN: ${{ secrets.CHROME_REFRESH_TOKEN }}
