name: Sync to Codeberg
concurrency:
    group: ${{ github.ref }}
    cancel-in-progress: true
on:
    push:
    workflow_dispatch:
    schedule:
        - cron: "0 */6 * * *"

jobs:
    codeberg:
        if: github.repository == 'Vendicated/Vencord'
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 0
            - uses: pixta-dev/repository-mirroring-action@674e65a7d483ca28dafaacba0d07351bdcc8bd75 # v1.1.1
              with:
                  target_repo_url: "****************:Vee/cord.git"
                  ssh_private_key: ${{ secrets.CODEBERG_SSH_PRIVATE_KEY }}
