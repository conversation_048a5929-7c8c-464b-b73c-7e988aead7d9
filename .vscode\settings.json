{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "javascript.format.semicolons": "insert", "typescript.format.semicolons": "insert", "typescript.preferences.quoteStyle": "double", "javascript.preferences.quoteStyle": "double", "gitlens.remotes": [{"domain": "codeberg.org", "type": "<PERSON><PERSON><PERSON>"}]}