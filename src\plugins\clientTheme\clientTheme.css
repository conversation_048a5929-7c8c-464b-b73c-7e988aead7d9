.vc-clientTheme-settings {
    display: flex;
    flex-direction: column;
}

.vc-clientTheme-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.vc-clientTheme-labels {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.vc-clientTheme-container [class^="swatch"] {
    border: thin solid var(--background-modifier-accent) !important;
}

.vc-clientTheme-buttons-container {
    margin-top: 16px;
    display: flex;
    gap: 4px;
}
