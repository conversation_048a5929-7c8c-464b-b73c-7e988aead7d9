name: Blank Issue
description: Create a blank issue. ALWAYS FIRST USE OUR SUPPORT CHANNEL! ONLY USE THIS FORM IF YOU ARE A CONTRIBUTOR OR WERE TOLD TO DO SO IN THE SUPPORT CHANNEL.

body:
    - type: markdown
      attributes:
          value: |
              ![Are you a developer? No? This form is not for you!](https://github.com/Vendicated/Vencord/blob/main/.github/ISSUE_TEMPLATE/developer-banner.png?raw=true)

              GitHub Issues are for development, not support! Please use our [support server](https://vencord.dev/discord) unless you are a Vencord Developer.

    - type: textarea
      id: content
      attributes:
          label: Content
      validations:
          required: true

    - type: checkboxes
      id: agreement-check
      attributes:
          label: Request Agreement
          options:
              - label: I have read the requirements for opening an issue above
                required: true
