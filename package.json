{"name": "vencord", "private": "true", "version": "1.12.3", "description": "The cutest Discord client mod", "homepage": "https://github.com/Vendicated/Vencord#readme", "bugs": {"url": "https://github.com/Vendicated/Vencord/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Vendicated/Vencord.git"}, "license": "GPL-3.0-or-later", "author": "Vendicated", "scripts": {"build": "node --require=./scripts/suppressExperimentalWarnings.js scripts/build/build.mjs", "buildStandalone": "pnpm build --standalone", "buildWeb": "node --require=./scripts/suppressExperimentalWarnings.js scripts/build/buildWeb.mjs", "buildWebStandalone": "pnpm buildWeb --standalone", "buildReporter": "pnpm buildWebStandalone --reporter --skip-extension", "buildReporterDesktop": "pnpm build --reporter", "watch": "pnpm build --watch", "dev": "pnpm watch", "watchWeb": "pnpm buildWeb --watch", "generatePluginJson": "tsx scripts/generatePluginList.ts", "generateTypes": "tspc --emitDeclarationOnly --declaration --outDir packages/vencord-types --allowJs false", "inject": "node scripts/runInstaller.mjs -- --install", "uninject": "node scripts/runInstaller.mjs -- --uninstall", "lint": "eslint", "lint-styles": "stylelint \"src/**/*.css\" --ignore-pattern src/userplugins", "lint:fix": "pnpm lint --fix", "test": "pnpm buildStandalone && pnpm testTsc && pnpm lint && pnpm lint-styles && pnpm generatePluginJson", "testWeb": "pnpm lint && pnpm buildWeb && pnpm testTsc", "testTsc": "tsc --noEmit"}, "dependencies": {"@intrnl/xxhash64": "^0.1.2", "@vap/core": "0.0.12", "@vap/shiki": "0.10.5", "buttplug": "^3.2.2", "fflate": "^0.8.2", "gifenc": "github:mattdesl/gifenc#64842fca317b112a8590f8fef2bf3825da8f6fe3", "monaco-editor": "^0.52.2", "nanoid": "^5.1.5", "virtual-merge": "^1.0.1"}, "devDependencies": {"@stylistic/eslint-plugin": "^4.2.0", "@types/chrome": "^0.0.312", "@types/diff": "^7.0.2", "@types/lodash": "^4.17.14", "@types/node": "^22.13.13", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/yazl": "^2.4.5", "diff": "^7.0.0", "discord-types": "^1.3.26", "esbuild": "^0.25.1", "eslint": "9.20.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-path-alias": "2.1.0", "eslint-plugin-react": "^7.37.3", "eslint-plugin-simple-header": "^1.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "highlight.js": "11.11.1", "html-minifier-terser": "^7.2.0", "moment": "^2.22.2", "puppeteer-core": "^24.4.0", "standalone-electron-types": "^34.2.0", "stylelint": "^16.17.0", "stylelint-config-standard": "^37.0.0", "ts-patch": "^3.3.0", "ts-pattern": "^5.6.0", "tsx": "^4.19.3", "type-fest": "^4.38.0", "typescript": "^5.8.2", "typescript-eslint": "^8.28.0", "typescript-transform-paths": "^3.5.5", "zip-local": "^0.3.5"}, "packageManager": "pnpm@10.4.1", "pnpm": {"patchedDependencies": {"eslint@9.20.1": "patches/<EMAIL>", "eslint-plugin-path-alias@2.1.0": "patches/<EMAIL>"}, "peerDependencyRules": {"ignoreMissing": ["eslint-plugin-import", "eslint"]}, "allowedDeprecatedVersions": {"source-map-resolve": "*", "resolve-url": "*", "source-map-url": "*", "urix": "*", "q": "*"}, "onlyBuiltDependencies": ["esbuild"]}, "engines": {"node": ">=18"}}