name: Build DevBuild
on:
    push:
        branches:
            - main
        paths:
            - .github/workflows/build.yml
            - src/**
            - browser/**
            - scripts/build/**
            - package.json
            - pnpm-lock.yaml
env:
    FORCE_COLOR: true

jobs:
    Build:
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v4

            - uses: pnpm/action-setup@v3 # Install pnpm using packageManager key in package.json

            - name: Use Node.js 20
              uses: actions/setup-node@v4
              with:
                  node-version: 20
                  cache: "pnpm"

            - name: Install dependencies
              run: pnpm install --frozen-lockfile

            - name: Build web
              run: pnpm buildWebStandalone

            - name: Build
              run: pnpm build --standalone

            - name: Generate plugin list
              run: pnpm generatePluginJson dist/plugins.json dist/plugin-readmes.json

            - name: Clean up obsolete files
              run: |
                  rm -rf dist/*-unpacked dist/vendor Vencord.user.css vencordDesktopRenderer.css vencordDesktopRenderer.css.map

            - name: Get some values needed for the release
              id: release_values
              run: |
                  echo "release_tag=$(git rev-parse --short HEAD)" >> $GITHUB_ENV

            - name: Upload DevBuild as release
              if: github.repository == 'Vendicated/Vencord'
              run: |
                  gh release upload devbuild --clobber dist/*
                  gh release edit devbuild --title "DevBuild $RELEASE_TAG"
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
                  RELEASE_TAG: ${{ env.release_tag }}

            - name: Upload DevBuild to builds repo
              if: github.repository == 'Vendicated/Vencord'
              run: |
                  git config --global user.name "$USERNAME"
                  git config --global user.email <EMAIL>

                  git clone https://$USERNAME:$<EMAIL>/$GH_REPO.git upload
                  cd upload

                  GLOBIGNORE=.git:.gitignore:README.md:LICENSE
                  rm -rf *
                  cp -r ../dist/* .

                  git add -A
                  git commit -m "Builds for https://github.com/$GITHUB_REPOSITORY/commit/$GITHUB_SHA"
                  git push --force https://$USERNAME:$<EMAIL>/$GH_REPO.git
              env:
                  API_TOKEN: ${{ secrets.BUILDS_TOKEN }}
                  GH_REPO: Vencord/builds
                  USERNAME: GitHub-Actions
